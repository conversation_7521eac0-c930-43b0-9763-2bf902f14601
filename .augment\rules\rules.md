---
type: "always_apply"
---

# 爬虫项目架构与功能规则文档

## 项目概述
这是一个智能网页文章采集器项目，具有以下核心特性：
- 基于 PyQt5 的图形界面
- AI 智能选择器分析
- 模组化配置管理
- 动态翻页处理
- 失败 URL 重试机制
- 多格式数据导出（CSV/Excel）

## 核心架构

### 1. 主要模块结构
```
crawler_project/
├── main.py                     # 主入口文件
├── core/                       # 核心爬虫功能
├── gui/                        # GUI界面模块  
├── ai/                         # AI分析模块
├── modules/                    # 模组管理
├── config/                     # 配置管理
├── testing/                    # 测试功能
├── utils/                      # 工具模块
└── configs/                    # 配置文件
```

### 2. 核心模块功能

#### core/ - 核心爬虫功能
- **crawler.py**: 主爬虫引擎，包含异步爬取逻辑
  - `crawl_articles_async()`: 主要的异步爬取函数
  - `save_article_async()`: 异步保存文章
  - `process_articles_batch()`: 批量处理文章
  - 支持多选择器（title_selectors, content_selectors, date_selectors, source_selectors）
  - 集成模组配置系统和字段配置系统

- **field_extractor.py**: 字段提取器
  - `FieldExtractor`: 灵活的字段提取类
  - 支持多种提取器：text_extractor, date_extractor, url_extractor 等
  - 可配置字段映射和默认值

- **excel_writer.py**: Excel 数据写入器
  - `ExcelWriter`: 支持多种写入模式的 Excel 写入类
  - 支持批量写入、智能写入、混合写入模式
  - 异步写入支持，文件级锁定机制

- **failed_url_processor.py**: 失败 URL 处理器
  - `FailedUrlProcessor`: 处理爬取失败的 URL
  - 支持批量重试、模组配置应用
  - 详细的失败原因记录

- **PaginationHandler.py**: 分页处理器
  - 支持点击翻页、滚动翻页、iframe 翻页
  - 动态翻页检测和处理

#### gui/ - GUI界面模块
- **main_window.py**: 主窗口界面
  - `CrawlerGUI`: 主GUI类，包含所有界面逻辑
  - 多标签页设计：基本配置、高级配置、模组配置、AI分析
  - 集成窗口状态管理

- **config_manager.py**: GUI配置管理
  - `GUIConfigManager`: GUI配置管理器
  - 配置验证、转换、存储功能
  - 默认配置管理

- **crawler_thread.py**: 爬虫线程管理
  - `CrawlerThread`: 支持异步的爬虫线程
  - `CrawlerThreadManager`: 线程管理器
  - 支持传统翻页和动态翻页

- **utils.py**: GUI工具函数
  - 应用样式表定义
  - 消息框工具函数
  - 配置验证器

#### ai/ - AI分析模块
- **analyzer.py**: AI选择器分析器
  - `AIAnalyzerWithTesting`: AI分析器主类
  - 支持列表页和文章页选择器分析
  - 集成选择器测试功能

- **helper.py**: AI助手功能
  - `EnhancedAIConfigManager`: AI配置管理器
  - AI配置对话框和测试功能

#### modules/ - 模组管理
- **manager.py**: 模组管理器
  - `ModuleManager`: 模组配置管理核心类
  - URL匹配、配置合并、优先级管理
  - 支持域名匹配和正则表达式匹配

#### config/ - 配置管理
- **manager.py**: 基础配置管理
- **unified_manager.py**: 统一配置管理器

### 3. 配置文件结构

#### configs/ - 配置文件目录
```
configs/
├── app/                     # 应用配置
│   ├── config.json         # 主应用配置
│   └── myconfig.json       # 用户配置
├── modules/                 # 模组配置
│   └── module_configs.json # 模组配置文件
├── ai/                      # AI配置
│   └── llm_config.json     # LLM配置
├── crawler/                 # 爬虫配置
│   └── crawler_config.json # 爬虫配置
├── fields/                  # 字段配置
│   └── field_configs.json  # 字段配置文件
└── backup/                  # 配置备份
```

## 重要函数和变量

### 核心爬虫函数
- `crawl_articles_async()`: 主异步爬取函数，支持所有爬取模式
- `save_article_async()`: 异步保存单篇文章
- `process_articles_batch()`: 批量处理文章列表
- `get_article_links_playwright()`: 使用 Playwright 获取文章链接

### 关键参数
- `content_selectors`: 内容选择器列表
- `title_selectors`: 标题选择器列表  
- `date_selectors`: 日期选择器列表
- `source_selectors`: 来源选择器列表
- `mode`: 爬取模式 ("fast", "safe", "balance")
- `use_module_config`: 是否使用模组配置
- `field_preset`: 字段预设名称
- `file_format`: 导出格式 ("CSV", "Excel")

### 模组配置系统
- `module_manager`: 全局模组管理器实例
- `get_config_for_url()`: 根据URL获取配置
- `match_module_for_url()`: URL模组匹配

### AI分析功能
- `AIAnalyzerWithTesting`: AI分析器主类
- `full_analysis_with_testing()`: 完整分析流程
- `analyze_with_field_config()`: 字段配置增强分析

## 数据流程

### 1. 爬取流程
1. GUI收集用户配置
2. 配置验证和转换
3. 模组配置匹配和合并
4. 启动爬虫线程
5. 异步爬取文章列表
6. 批量处理文章内容
7. 数据清洗和格式化
8. 导出到文件

### 2. 模组配置流程
1. URL输入
2. 模组匹配（域名/正则）
3. 配置合并（模组+全局）
4. 应用到爬虫参数

### 3. AI分析流程
1. 页面数据获取
2. AI模型分析
3. 选择器生成
4. 选择器测试验证
5. 配置生成和应用

## 重要设计模式

### 1. 异步处理
- 所有爬取操作都支持异步
- 使用 asyncio 进行并发控制
- 线程安全的数据写入

### 2. 模组化配置
- 基于URL的智能配置匹配
- 配置继承和覆盖机制
- 优先级排序

### 3. 多选择器支持
- 每个字段支持多个选择器
- 按优先级依次尝试
- 提高爬取成功率

### 4. 错误处理和重试
- 失败URL记录和重试
- 多级错误处理
- 详细的日志记录

## 扩展点

### 1. 新增模组
- 在 `configs/modules/module_configs.json` 中添加配置
- 定义URL匹配规则和选择器

### 2. 自定义字段提取器
- 在 `FieldExtractor` 中添加新的提取器方法
- 支持复杂的数据处理逻辑

### 3. 新的爬取模式
- 在 `crawler.py` 中扩展 mode 参数
- 添加新的爬取策略

### 4. AI模型集成
- 在 `ai/analyzer.py` 中集成新的AI模型
- 扩展选择器分析能力

## 详细模块分析

### core/crawler.py 核心函数详解

#### 主要异步函数
```python
async def crawl_articles_async(
    all_articles=None,           # 预定义文章列表
    input_url=None,              # 列表页URL
    base_url=None,               # 基础URL
    max_pages=None,              # 最大页数
    list_container_selector=".main",  # 列表容器选择器
    article_item_selector=".clearfix.ty_list li a",  # 文章项选择器
    title_selectors=None,        # 标题选择器列表
    date_selectors=None,         # 日期选择器列表
    source_selectors=None,       # 来源选择器列表
    content_selectors=[...],     # 内容选择器列表
    content_type="CSS",          # 选择器类型
    mode="balance",              # 爬取模式
    use_module_config=True,      # 是否使用模组配置
    field_preset=None,           # 字段预设
    custom_field_list=None,      # 自定义字段列表
    user_custom_fields=None,     # 用户自定义字段
    use_field_config=False       # 是否使用字段配置
)
```

#### 爬取模式说明
- **fast**: 仅使用 requests + BeautifulSoup（快速但可能失败）
- **safe**: 仅使用 Playwright（安全但较慢）
- **balance**: requests 优先，失败后使用 Playwright（推荐）

#### 批量处理函数
```python
async def process_articles_batch(
    all_articles,                # 文章信息列表
    content_selectors,           # 内容选择器
    max_workers=5,               # 最大并发数
    log_callback=None,           # 日志回调
    progress_callback=None,      # 进度回调
    use_module_config=True       # 模组配置开关
)
```

### core/field_extractor.py 字段提取系统

#### FieldExtractor 类方法
```python
class FieldExtractor:
    def configure_fields(self, field_configs)  # 配置字段
    def add_custom_extractor(self, name, func)  # 添加自定义提取器
    async def extract_fields(self, page, url, content_html, static_values)  # 提取所有字段
    def get_headers(self)  # 获取表头
```

#### 内置提取器类型
- `text_extractor`: 文本内容提取
- `date_extractor`: 日期格式化提取
- `url_extractor`: URL提取
- `content_extractor`: 内容提取（需要外部传入）
- `static_extractor`: 静态值提取
- `timestamp_extractor`: 时间戳提取

### modules/manager.py 模组管理系统

#### ModuleManager 核心方法
```python
class ModuleManager:
    def match_url(self, url)  # URL匹配
    def get_config_for_url(self, url, global_config)  # 获取配置
    def add_module(self, module_name, module_config)  # 添加模组
    def delete_module(self, module_name)  # 删除模组
    def merge_with_global_config(self, module_config, global_config)  # 配置合并
```

#### 模组配置结构
```json
{
    "模组名称": {
        "name": "模组名称",
        "description": "模组描述",
        "domain_patterns": ["domain1.com", "domain2.com"],
        "url_patterns": ["regex_pattern1", "regex_pattern2"],
        "config": {
            "title_selectors": [...],
            "content_selectors": [...],
            "mode": "safe",
            "retry": 3,
            "interval": 2.0
        }
    }
}
```

### ai/analyzer.py AI分析系统

#### AIAnalyzerWithTesting 主要方法
```python
class AIAnalyzerWithTesting:
    async def full_analysis_with_testing(self, url)  # 完整分析
    async def analyze_list_page_selectors(self, url)  # 列表页分析
    async def analyze_article_page_selectors(self, url)  # 文章页分析
    async def analyze_with_field_config(self, url, field_preset, custom_fields)  # 字段配置分析
```

#### AI配置参数
```json
{
    "api_key": "your_api_key",
    "base_url": "https://api.deepseek.com/v1",
    "model": "deepseek-chat",
    "temperature": 0.3,
    "max_tokens": 1500,
    "enable_ai": true
}
```

### gui/main_window.py GUI主窗口

#### CrawlerGUI 主要组件
- 基本配置标签页：URL、选择器、基础参数
- 高级配置标签页：翻页、过滤器、导出设置
- 模组配置标签页：模组管理、失败URL处理
- AI分析标签页：智能选择器分析

#### 重要方法
```python
class CrawlerGUI:
    def init_ui(self)  # 初始化界面
    def start_crawling(self)  # 开始爬取
    def stop_crawling(self)  # 停止爬取
    def save_config(self)  # 保存配置
    def load_config(self)  # 加载配置
```

## 配置文件详解

### configs/modules/module_configs.json
模组配置的核心文件，定义了不同网站的专用配置：
- 微信公众号模组：专门处理微信文章
- 可扩展添加其他网站的专用配置

### configs/fields/field_configs.json
字段配置文件，定义了数据提取的字段结构：
- default_fields：默认字段配置
- 支持自定义字段和提取器
- 字段类型：text, html, url, number, date

### configs/ai/llm_config.json
AI模型配置：
- API密钥和端点配置
- 模型参数设置
- 启用/禁用AI功能

## 数据处理流程

### 1. URL收集阶段
1. 解析列表页URL
2. 使用list_container_selector定位列表容器
3. 使用article_item_selector提取文章链接
4. 支持多页翻页（传统翻页/动态翻页）
5. URL去重和缓存

### 2. 内容提取阶段
1. 并发访问文章页面
2. 应用模组配置（如果匹配）
3. 使用多选择器提取内容
4. 字段提取器处理数据
5. 文本清洗和格式化

### 3. 数据存储阶段
1. 数据验证和清洗
2. 批量写入Excel/CSV
3. 失败URL记录
4. 进度跟踪和日志记录

## 错误处理机制

### 1. 网络错误处理
- 连接超时重试
- HTTP状态码检查
- 代理轮换（如配置）

### 2. 选择器失败处理
- 多选择器备选机制
- 默认值填充
- 错误日志记录

### 3. 数据写入错误处理
- 文件锁定机制
- 写入重试
- 备份机制

## 性能优化

### 1. 并发控制
- max_workers参数控制并发数
- 异步IO提高效率
- 内存使用优化

### 2. 缓存机制
- URL去重缓存
- 页面内容缓存
- 配置缓存

### 3. 批量处理
- 批量数据写入
- 批量URL处理
- 智能写入模式

## 扩展开发指南

### 1. 添加新的提取器
```python
async def custom_extractor(self, field_name, config, page, url, soup):
    # 自定义提取逻辑
    return extracted_value

# 注册提取器
field_extractor.add_custom_extractor('custom_extractor', custom_extractor)
```

### 2. 添加新的模组
```python
new_module = {
    "name": "新网站",
    "description": "新网站的配置",
    "domain_patterns": ["newsite.com"],
    "config": {
        "title_selectors": [".title"],
        "content_selectors": [".content"]
    }
}
module_manager.add_module("新网站", new_module)
```

### 3. 自定义AI分析
```python
# 在ai/analyzer.py中扩展
async def custom_analysis(self, url):
    # 自定义分析逻辑
    return analysis_result
```

## 常用工具函数

### core/excel_writer.py
```python
class ExcelWriter:
    def write_direct(self, file_path, data_row, headers)  # 直接写入
    def write_batch(self, file_path, data_rows, headers)  # 批量写入
    def write_smart(self, file_path, data_row, headers)   # 智能写入
    async def write_async(self, file_path, data_row, headers)  # 异步写入
```

### utils/text_cleaner.py
```python
def clean_text(text)  # 基础文本清洗
def clean_non_text_fields(data_dict)  # 非文本字段清洗
def remove_html_tags(text)  # 移除HTML标签
def normalize_whitespace(text)  # 标准化空白字符
```

### gui/utils.py
```python
def get_application_stylesheet()  # 获取应用样式表
def show_info_message(parent, title, message)  # 显示信息消息
def show_error_message(parent, title, message)  # 显示错误消息
def validate_selector(selector, field_name)  # 验证选择器
```

## 测试和调试

### testing/selectors_test.py
```python
class SelectorsTestManager:
    async def test_selectors(self, url, selectors)  # 测试选择器
    def validate_selector_syntax(self, selector)    # 验证选择器语法
    def generate_test_report(self, results)         # 生成测试报告
```

### 调试技巧
1. 启用详细日志记录
2. 使用选择器测试功能
3. 检查模组配置匹配
4. 验证字段提取器配置
5. 监控异步任务状态

## 部署和维护

### 1. 环境要求
- Python 3.8+
- PyQt5
- Playwright
- BeautifulSoup4
- openpyxl
- requests

### 2. 配置管理
- 定期备份配置文件
- 版本控制配置变更
- 监控AI API使用量

### 3. 性能监控
- 监控爬取成功率
- 跟踪处理时间
- 检查内存使用情况

### 4. 故障排除
- 检查网络连接
- 验证选择器有效性
- 确认模组配置正确性
- 查看详细错误日志

### 5.重要注意事项
- 不要乱改其他文件功能。
- 最后动作是要打开main.py进行测试。 
